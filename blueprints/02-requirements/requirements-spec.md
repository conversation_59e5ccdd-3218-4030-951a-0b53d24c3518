
# 需求

Aone 地址： https://project.aone.alibaba-inc.com/v2/project/2058340/req/65580309

## 物料名

产品详情-常见问题

## 功能描述

实现如下功能点：

1. 使用标签页展示分类列表， 每个分类下展示一个常见问题的问答对列表， 每个问答对，支持一个跳转链接
2. 展示一个跳转类卡片

## 数据结构和配置说明

1. 支持配置多个问答类型(至少3个，最多5个)， 每个问答类型下包含类型名称和至少 3 个问答对
2. 每个问答对包含一个问题和一个答案 (均为字符串类型)
3. 跳转卡片支持配置 标题 和 跳转链接 两个字段

## UI 和交互细节

1. 设计稿地址： https://mgdone.alibaba-inc.com/file/162733819736234?page_id=M&devMode=true&layer_id=158%3A01827
2. 卡片使用固定的图标，hover 有阴影过渡效果
3. 标签页和卡片采用响应式布局，宽屏左右结构，移动端下使用单列布局

## 开发说明

1. 项目主要 @ali/adc 使用Container组件来实现整个物料的响应式布局，右侧卡片使用固定宽度，左侧常见问题tabs自适应剩余宽度
2. schema 编写注意事项：
   a. 常见问题的分类是一个列表，分类名自定义，问题列表从信息中心中匹配
   b. 常见问题的问题列表，从 产品详情-常见问题 模型获取，注意只保留问题、回答、跳转链接 这3个字段
   c. 右侧跳转卡片，图标为固定展示项，不需要进行配置
3. 开发说明：
   a. mock数据尽可能从信息中心模型里获取真实数据
   b. 布局和组件实现，优先按照设计稿 DSL 里的层次来处理
