# 开发指引

COM Design 设计系统，是阿里云官网开发的一套设计系统，包含基础组件和设计规范。

## 开始接入

### 1. 安装 npm 包

```bash
# 安装基础组件库
$ tnpm install @ali/adc --save
```

### 2. 在项目中使用

```jsx
import { Button } from '@ali/adc';

import '@ali/adc/es/index.css';

export default () => {
  return <Button>按钮</Button>;
};
```

## 预设说明

- `className`、`style` 等常见属性，未在组件文档中注明，可直接使用。

## 布局

布局主要使用 `Container` 组件，通常是物料的最外层容器，会自动处理响应式。
如果一个物料已经明确会放在另外一个支持响应式的容器内，其内容宽度明确占满父容器的 100%，那么可以直接使用 `Box` 组件。

如果有一行多列的布局，可以使用 `Flex` 组件，其子元素使用 `Box` 来设置布局。
如果是多行多列的布局，可以使用 `Grid` 组件，其子元素使用 `Box` 来设置布局。

## 关于移动端和超大屏幕的设置方法

com-design 对于小屏幕和超大屏幕有覆盖式属性设置。

- 小屏幕下，通过 `sm` 属性设置额外或覆盖已有属性：

```jsx
// 指定小屏幕下，padding-top 的 spacing 值为 2 (实际为 8px)
<Box pt={4} sm={{ pt: 2 }}></Box>
```

- 超宽屏幕下，通过 `lg` 属性设置额外或覆盖已有属性：

```jsx
// 指定超宽屏幕下，padding-top 的 spacing 值为 6 (实际为 24px)
<Box pt={4} lg={{ pt: 6 }}></Box>
```

## 术语表

| 词汇       | 含义                                                           |
| ---------- | -------------------------------------------------------------- |
| COM-Design | 代表阿里云官网(aliyun.com)的设计系统(Design System)            |
| adc        | 同样指代 COM-Design，通常指设计系统对应的基础组件库 `@ali/adc` |

## 常见用法

### 双按钮

宽屏上水平并列，窄屏上垂直且撑满父容器。

```jsx
import { Button, Flex } from '@ali/adc';

export default () => {
  return (
    <Flex gap="md" sm={{ vertical: true }}>
      <Button type="solid" size="hero" sm={{ block: true }} color="subtlest">
        立即购买
      </Button>
      <Button type="default" size="hero" sm={{ block: true }}>
        免费试用
      </Button>
    </Flex>
  );
};
```

### 一排三卡片

带有阴影的一排三卡片，移动端采用垂直小间隙布局。

```jsx
import { Box, Button, Grid } from '@ali/adc';

export default () => {
  return (
    <Grid cols={3} gap="xl" sm={{ gap: 'lg', cols: 1 }}>
      <Box bordered shadowed p="md">
        卡片1
      </Box>
      <Box bordered shadowed p="md">
        卡片2
      </Box>
      <Box bordered shadowed p="md">
        卡片3
      </Box>
    </Grid>
  );
};
```

### 标签页

常用的卡片类标签页，移除水平内间距。

```jsx
import { Box, Button, Flex, Grid, Tabs, Typography } from '@ali/adc';

const { Text, H4 } = Typography;

export default () => {
  return (
    <Tabs>
      <Tabs.List>
        <Tabs.Tab>标签一</Tabs.Tab>
        <Tabs.Tab>标签二</Tabs.Tab>
        <Tabs.Tab>标签三</Tabs.Tab>
      </Tabs.List>
      <Tabs.Panels px={0} pb={0}>
        <Tabs.Panel>
          <Grid cols={3} gap="xl" sm={{ gap: 'lg', cols: 1 }}>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>高性能处理</H4>
              <Text color="subtlest">
                ClickHouse
                作为列式数据库，具有高性能的数据查询和分析能力。相比于其他 HTAP
                解决方案，ClickHouse
                可以在短时间内处理大量数据，提高数据处理效率。
              </Text>
            </Box>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>高性能处理</H4>
              <Text color="subtlest">
                ClickHouse
                作为列式数据库，具有高性能的数据查询和分析能力。相比于其他 HTAP
                解决方案，ClickHouse
                可以在短时间内处理大量数据，提高数据处理效率。
              </Text>
            </Box>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>高性能处理</H4>
              <Text color="subtlest">
                ClickHouse
                作为列式数据库，具有高性能的数据查询和分析能力。相比于其他 HTAP
                解决方案，ClickHouse
                可以在短时间内处理大量数据，提高数据处理效率。
              </Text>
            </Box>
          </Grid>
        </Tabs.Panel>
        <Tabs.Panel>
          <Grid cols={3} gap="xl" sm={{ gap: 'lg', cols: 1 }}>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>实时数据同步</H4>
              <Text color="subtlest">
                MaterializedMySQL 实现 MySQL 数据到 ClickHouse
                的实时同步，保证数据的实时性和一致性。相比于其他 HTAP
                解决方案，MaterializedMySQL
                可以实时同步数据，降低数据延迟，提高数据准确性。
              </Text>
            </Box>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>实时数据同步</H4>
              <Text color="subtlest">
                MaterializedMySQL 实现 MySQL 数据到 ClickHouse
                的实时同步，保证数据的实时性和一致性。相比于其他 HTAP
                解决方案，MaterializedMySQL
                可以实时同步数据，降低数据延迟，提高数据准确性。
              </Text>
            </Box>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>实时数据同步</H4>
              <Text color="subtlest">
                MaterializedMySQL 实现 MySQL 数据到 ClickHouse
                的实时同步，保证数据的实时性和一致性。相比于其他 HTAP
                解决方案，MaterializedMySQL
                可以实时同步数据，降低数据延迟，提高数据准确性。
              </Text>
            </Box>
          </Grid>
        </Tabs.Panel>
        <Tabs.Panel>
          <Grid cols={3} gap="xl" sm={{ gap: 'lg', cols: 1 }}>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>追求极致的易用体验</H4>
              <Text color="subtlest">
                MySQL+ClickHouse 解决方案可以无缝集成到现有的 MySQL
                数据库环境中，降低迁移成本。相比于其他 HTAP
                解决方案，MySQL+ClickHouse
                解决方案的部署和使用更加简单，降低了学习成本。
              </Text>
            </Box>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>追求极致的易用体验</H4>
              <Text color="subtlest">
                MySQL+ClickHouse 解决方案可以无缝集成到现有的 MySQL
                数据库环境中，降低迁移成本。相比于其他 HTAP
                解决方案，MySQL+ClickHouse
                解决方案的部署和使用更加简单，降低了学习成本。
              </Text>
            </Box>
            <Box bordered shadowed gap="xs" p="lg">
              <H4>追求极致的易用体验</H4>
              <Text color="subtlest">
                MySQL+ClickHouse 解决方案可以无缝集成到现有的 MySQL
                数据库环境中，降低迁移成本。相比于其他 HTAP
                解决方案，MySQL+ClickHouse
                解决方案的部署和使用更加简单，降低了学习成本。
              </Text>
            </Box>
          </Grid>
        </Tabs.Panel>
      </Tabs.Panels>
    </Tabs>
  );
};
```
