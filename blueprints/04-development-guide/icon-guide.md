# 使用图标

## 使用步骤

### 确认要使用的图标的名称和npm包
- 如果有明确使用的图标和 npm 包，则直接按照 npm 包的对应 api 使用
- 如果没有明确指定使用哪个图标，从 `icon-list.csv` 中搜索并确认要使用的图标
  - 基于查找到的图标类型(`iconType` 字段)，判断使用哪一个 npm 包, 判定规则如下：
    - global: 通用类图标, 包括箭头、社交活动、电商、用户、编辑、通知消息等 `@ali/adc-icons`
    - biz: 与业务相关的图标，如产品详情、文档、解决方案等 `@ali/adc-biz-icons`
    - product:与产品相关的图标, 如ECS、RDS、OSS等具体云产品图标 `@ali/adc-product-icons`
  - 确定对应等图标 `fullName` ，确认组件调用名称

## 使用示例

从 `icon-list.json` 中的信息中，确认要使用空心向右箭头时， 确认iconType 为 global, 使用 `@ali/adc-icons` 包。确认icon组件的全名(fullName) `ArrowRightOutlined`。

因此，调用代码如下：

```jsx
import { ArrowRightOutlined } from '@ali/adc-icons';

export default () => {
  // 使用className定制相关的图标样式，注意使用 font-size 属性来调整图标展示尺寸
  return <ArrowRightOutlined className="custom-icon" />;
};
```

# 注意事项

- 不能直接使用 `@ali/adc-icons` 导出的 `Icon` 组件，它仅在需要自定义传入 svg 图标时使用
