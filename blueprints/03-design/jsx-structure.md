# 组件结构 JSX 简写示例

```jsx
<Container>
  <Flex columns={2}>
    <Tabs size="lg">
      <Tabs.List>
        <Tabs.Tab>Tab 1</Tabs.Tab>
        <Tabs.Tab>Tab 2</Tabs.Tab>
        <Tabs.Tab>Tab 3</Tabs.Tab>
        <Tabs.Tab>Tab 4</Tabs.Tab>
      </Tabs.List>
      <Tabs.Panels>
        <Tabs.Panel>
          <Typography.H6>Q：</Typography.H6>
          <Typography.Text>
            RDS MySQL 与自建 MySQL 数据库对比优势
          </Typography.Text>
          <Typography.H6>A：</Typography.H6>
          <Typography.Text color="subtlest">
            云数据库 RDS MySQL
            提供高可用、高可靠、高安全、可扩展的托管数据库服务...
          </Typography.Text>
        </Tabs.Panel>
        <Tabs.Panel>
          <Typography.H6>Q：</Typography.H6>
          <Typography.Text>
            基础系列、高可用系列、集群系列应该怎么选？
          </Typography.Text>
          <Typography.H6>A：</Typography.H6>
          <Typography.Text color="subtlest">
            基础版为单节点架构...
          </Typography.Text>
        </Tabs.Panel>
        <Tabs.Panel>
          <Typography.H6>Q：</Typography.H6>
          <Typography.Text>RDS 存在什么使用限制和注意事项吗？</Typography.Text>
          <Typography.H6>A：</Typography.H6>
          <Typography.Text color="subtlest">
            在变更配置、版本升级、故障切换时存在闪断...
          </Typography.Text>
        </Tabs.Panel>
      </Tabs.Panels>
    </Tabs>
    <Box shadowed>
      <Space vertical>
        <Box>
          <Ackdistro />
        </Box>
        <Box>
          <Typography.H3>
            您的RDS问题得不到解答？来了解更多常见问题，找到您的答案
          </Typography.H3>
        </Box>
        <Box>
          <ArrowRightOutlined />
        </Box>
      </Space>
    </Box>
  </Flex>
</Container>
```
