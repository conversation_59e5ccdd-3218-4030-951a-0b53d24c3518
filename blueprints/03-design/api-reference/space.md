# Space

为行内元素自动增加间距，默认 8px。

## API

Space 容器继承 Flex 容器属性。

| 参数  | 说明         | 类型                                         | 默认值 |
| ----- | ------------ | -------------------------------------------- | ------ |
| size  | 间隙大小     | `BaseSpacingSize`                            | sm     |
| split | 间隙分隔符   | `ReactNode`                                  | -      |
| align | 垂直对齐位置 | `"start"`\|`"center"`\|`"end"`\|`"baseline"` | center |

## 设计

- Space 组件用于行内元素自动增加间距，默认 8px。
- 推荐与 Flex 组件组合使用，适合按钮组、标签组等场景。

## 使用示例

### 基础用法

展示 Space 布局的基本使用方式：

```jsx
/**
 * title: 间隙
 * description: 内敛元素自动加上间隙
 */
import { Box, Space } from '@ali/adc';

export default () => {
  return (
    <Space sm={{ vertical: true, split: null }}>
      <Box bg="success" width={40} height={40} centered>
        1
      </Box>
      <Box bg="info" width={40} height={40} centered>
        2
      </Box>
      <Box bg="error" width={40} height={40} centered>
        3
      </Box>
    </Space>
  );
};
```

### 响应式布局

在不同屏幕尺寸下动态调整间距的大小：

```jsx
/**
 * title: 间隙和响应式
 * description: 定制间隙，多元素支持自动换行
 */
import { Button, Space } from '@ali/adc';

export default () => {
  return (
    <Space className="twine" size="lg" sm={{ size: 'sm' }}>
      {Array.from(new Array(20)).map((_, index) => (
        <Button key={index} type="solid">
          button
        </Button>
      ))}
    </Space>
  );
};
```

### 分割线

通过 `split` 属性设置分隔符样式：

```jsx
/**
 * title: 分隔元素
 * description: 自定义分隔符
 */
import { Button, Space, Typography } from '@ali/adc';

const { Text } = Typography;

export default () => {
  return (
    <Space
      split={
        <Text color="subtlest" compact>
          |
        </Text>
      }>
      <Button type="link">立即购买</Button>
      <Button type="link">查看详情</Button>
    </Space>
  );
};
```

### 垂直间距

通过 `direction` 属性设置垂直方向的间距：

```jsx
/**
 * title: 垂直方向
 * description: 自定义垂直方向布局模式
 */
import { Button, Space } from '@ali/adc';

export default () => {
  return (
    <Space vertical>
      <Button type="outlined">立即购买</Button>
      <Button type="outlined">查看详情</Button>
    </Space>
  );
};
``` 