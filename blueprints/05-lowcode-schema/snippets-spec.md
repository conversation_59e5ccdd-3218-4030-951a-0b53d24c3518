# snippets 配置说明

## 1. 概述

`meta/snippets.json` 文件用于定义组件在低代码平台（如鸿蒙编辑器）中的初始 schema 配置。每个 snippet 代表组件的一种默认状态，用户拖拽组件到设计器时，会自动插入此处定义的 schema 数据。

## 2. 字段结构说明

snippets.json 文件内容为数组，每一项代表一种组件默认配置。结构如下：

```json
[
  {
    "title": "XXX",
    "screenshot": "https://img.alicdn.com/...",
    "snippet": {
      "props": {
        "name": "示例组件",
        "deliveryId": "2727"
      }
    }
  }
]
```

### 字段含义

- `title`：组件在物料面板中的显示名称（字符串）。
- `screenshot`：组件缩略图 URL，便于视觉识别（字符串，可选）。
- `snippet.props`：组件的默认入参，结构需与 schema 保持一致（对象）。
  - `props` 字段结构必须与组件 schema 定义保持一致，否则会导致低代码平台渲染异常。

## 3. 使用示例

### 单一配置示例

```json
[
  {
    "title": "示例组件",
    "screenshot": "https://img.alicdn.com/xxx.png",
    "snippet": {
      "props": {
        "name": "示例组件",
        "deliveryId": "2727"
      }
    }
  }
]
```

拖入组件后，页面 schema 默认插入：

```json
{ "name": "示例组件", "deliveryId": "2727" }
```

### 多状态配置示例

如一个卡片组件有两种样式：

```json
[
  {
    "title": "营销一排四条版小卡片",
    "screenshot": "https://img.alicdn.com/imgextra/i1/O1CN01dfkiPK1PARpsiL8ym_!!",
    "snippet": {
      "props": {
        "deliveryId": 2727,
        "cardStyle": "default",
        "pageSize": 8,
        "uiConfig": {}
      }
    }
  },
  {
    "title": "产详页营销条版小卡片",
    "screenshot": "https://img.alicdn.com/imgextra/i1/O1CN010VVhuZ1dT8cRSDrMU_!!",
    "snippet": {
      "props": {
        "deliveryId": 2727,
        "cardStyle": "product",
        "pageSize": 6,
        "uiConfig": {}
      }
    }
  }
]
```

这样在物料面板会出现两种可选卡片，用户可根据需求选择不同样式。

## 4. 注意事项

- `props` 字段结构必须与组件 schema 定义保持一致，否则会导致低代码平台渲染异常。
- 可配置多个 snippet，便于支持多种组件默认状态。
- `screenshot` 字段建议填写，提升物料面板的可用性和易用性。
- 仅需关注 `props` 配置，无需关心组件内部样式差异。

## 5. 低代码平台适配说明

- `snippets` 主要用于低代码场景，自动生成组件的初始 `schema`。
- 用户拖拽组件时，平台会自动插入 snippets.json 中定义的 props。
- 适用于鸿蒙编辑器等低代码平台。
