# 项目开发引导

## Blueprints 目录结构说明

本目录包含AI生成项目所需的相关知识和需求文档，按功能模块进行组织。

## 目录结构

```
blueprints/
├── 01-project-setup/          # 项目初始化设置
│   ├── metadata.json          # 项目基础配置信息
│   ├── initialization-guide.md        # 项目初始化指南
│   └── architecture-spec.md  # 项目架构规范
├── 02-requirements/           # 需求文档
│   └── requirements-spec.md  # 业务需求规范文档
├── 03-design/                 # 设计相关文档
│   ├── dsl.json              # 设计系统语言
│   ├── structure-spec.md      # 组件结构规范
│   └── api-reference.md  # UI组件API参考文档
├── 04-development-guide/      # 开发指南
│   ├── library-guide.md   # 组件库使用指导
│   ├── icon-guide.md   # 图标实现指南
│   └── template.zip          # 项目模板
├── 05-lowcode-schema/         # 低代码Schema相关
│   ├── schema-spec.md    # 低代码Schema规范
│   └── snippets-spec.md # 代码片段规范
├── 06-knowledge-base/         # 知识库
│   ├── foundation-guide.md            # 项目基础指南
│   ├── best-practices.md      # 开发最佳实践
│   └── doc-standards.md     # 文档编写标准
├── dev-workflow.md    # 物料开发流程指引
└── README.md                 # 本说明文件
```

## 文档说明

- **01-project-setup**: 包含项目初始化所需的配置文件和指南
- **02-requirements**: 存放业务需求规范和需求分析文档
- **03-design**: 设计系统相关的数据和API参考文档
- **04-development-guide**: 开发过程中的组件库指导和实现指南
- **05-lowcode-schema**: 低代码引擎相关的Schema和代码片段规范
- **06-knowledge-base**: 项目开发的基础知识和最佳实践标准
- **dev-workflow.md**: 完整的物料开发流程指引，是整个开发过程的主要参考文档

## 使用说明

1. 开始项目开发前，请先阅读 `dev-workflow.md`
2. 按照流程指引，依次参考各目录下的相关文档，严格按照步骤完成开发
