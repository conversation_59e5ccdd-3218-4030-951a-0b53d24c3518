# 最佳实践

## 组件开发原则

### 1. 组件层次结构
- 优先使用COM Design组件库(@ali/adc)中的组件
- 避免直接使用原生HTML标签，用对应的组件替代：
  - 用 `Box` 替代 `div`
  - 用 `Typography.Text` 替代 `span`
  - 用 `Typography.H1-H6` 替代 `h1-h6`
  - 用 `Typography.P` 替代 `p`

### 2. 响应式设计
- 所有组件都应考虑响应式适配
- 使用 `sm` 属性配置小屏幕下的样式
- 使用 `lg` 属性配置大屏幕下的样式

```jsx
// 响应式布局示例
<Flex gap="lg" sm={{ vertical: true, gap: 'sm' }}>
  <Box flex={1}>内容1</Box>
  <Box flex={1}>内容2</Box>
</Flex>
```

## 布局模式

### 1. 卡片布局
云官网的卡片通常可以使用 `Box` 组件布局，带有内置边框、阴影以及默认的内边距

**注意：** 卡片不要添加圆角样式

```jsx
<Box bordered shadowed p="md">
  卡片内容
</Box>
```

### 2. 左右布局（宽屏水平，移动端垂直）
```jsx
<Container>
  <Flex gap="xl" sm={{ vertical: true, gap: 'lg' }}>
    <Box flex={1}>
      {/* 左侧内容 */}
    </Box>
    <Box width={300} sm={{ width: '100%' }}>
      {/* 右侧固定宽度内容 */}
    </Box>
  </Flex>
</Container>
```

### 3. 标签页布局
```jsx
<Tabs sm={{ type: 'block', full: true }}>
  <Tabs.List>
    <Tabs.Tab>标签1</Tabs.Tab>
    <Tabs.Tab>标签2</Tabs.Tab>
  </Tabs.List>
  <Tabs.Panels px={0}>
    <Tabs.Panel>内容1</Tabs.Panel>
    <Tabs.Panel>内容2</Tabs.Panel>
  </Tabs.Panels>
</Tabs>
```

## 字体使用规范

### 1. 字体组件选择
所有的字体要尽可能使用 Typography 的子组件来定义，已经涵盖了 Banner， H1~H6 标题， 正文，描述文本等多类字体

### 2. 字体样式配置
字体的样式，尽可能使用 Typography 子组件的属性来定义

```jsx
// 最弱的颜色的文本，带下划线
<Typography.Text color="subtlest" underline>文本</Typography.Text>

// 标题层级使用
<Typography.H3>三级标题</Typography.H3>
<Typography.H4>四级标题</Typography.H4>

// 段落使用
<Typography.P color="subtlest">
  描述性文本内容
</Typography.P>
```

### 3. 文本颜色层级
```jsx
<Typography.Text>正文文本</Typography.Text>
<Typography.Text color="subtle">次级文本</Typography.Text>
<Typography.Text color="subtler">三级文本</Typography.Text>
<Typography.Text color="subtlest">四级文本</Typography.Text>
<Typography.Text disabled>禁用文本</Typography.Text>
```

## 间距和布局

### 1. 间距使用
- 使用 `Space` 组件处理元素间距
- 使用 `Box` 的 padding/margin 属性设置内外边距
- 间距值使用预设的 spacing 类型：`xs`, `sm`, `md`, `lg`, `xl`

```jsx
// 垂直间距
<Space vertical gap="md">
  <Box>元素1</Box>
  <Box>元素2</Box>
</Space>

// 水平间距
<Space gap="lg">
  <Button>按钮1</Button>
  <Button>按钮2</Button>
</Space>
```

### 2. 容器使用
```jsx
// 页面级容器
<Container>
  <Box p="lg">
    页面内容
  </Box>
</Container>

// 内容分组容器
<Box bordered shadowed p="md" mb="lg">
  分组内容
</Box>
```

## 交互和状态

### 1. 悬停效果
对于可交互的卡片，添加悬停阴影效果：

```jsx
<Box 
  bordered 
  shadowed 
  p="md" 
  className="cursor-pointer hover:shadow-lg transition-shadow"
>
  可点击卡片
</Box>
```

### 2. 加载和空状态
```jsx
// 加载状态
const [loading, setLoading] = useState(false);

// 空状态处理
{data.length === 0 ? (
  <Box centered p="xl">
    <Typography.Text color="subtlest">暂无数据</Typography.Text>
  </Box>
) : (
  // 数据展示
)}
```

## 数据处理

### 1. Props类型定义
```typescript
interface ComponentProps {
  title: string;
  items: Array<{
    question: string;
    answer: string;
    link?: string;
  }>;
  cardTitle?: string;
  cardLink?: string;
}
```

### 2. 默认值处理
```jsx
const Component: React.FC<ComponentProps> = ({
  title = '默认标题',
  items = [],
  cardTitle,
  cardLink,
}) => {
  // 组件实现
};
```

## 性能优化

### 1. 组件拆分
当主组件代码超过200行时，按照功能拆分子组件：

```jsx
// 主组件
export default function MainComponent(props) {
  return (
    <Container>
      <TabsSection data={props.tabsData} />
      <CardSection title={props.cardTitle} link={props.cardLink} />
    </Container>
  );
}

// 子组件
function TabsSection({ data }) {
  // 标签页实现
}

function CardSection({ title, link }) {
  // 卡片实现
}
```

### 2. 样式模块化
使用 CSS Modules 管理样式：

```jsx
import styles from './index.module.css';

<Box className={styles.customCard}>
  自定义样式卡片
</Box>
```

## 代码规范

### 1. 导入顺序
```jsx
// 1. React相关
import React, { useState, useEffect } from 'react';

// 2. 第三方库
import { Container, Box, Flex, Typography, Tabs } from '@ali/adc';

// 3. 本地组件和工具
import { CustomComponent } from './components';
import { formatData } from './utils';

// 4. 样式文件
import '@ali/adc/es/index.css';
import styles from './index.module.css';
```

### 2. 组件结构
```jsx
interface Props {
  // props类型定义
}

const Component: React.FC<Props> = (props) => {
  // 1. hooks
  const [state, setState] = useState();
  
  // 2. 计算属性
  const computedValue = useMemo(() => {}, []);
  
  // 3. 事件处理
  const handleClick = () => {};
  
  // 4. 渲染
  return (
    <Container>
      {/* JSX内容 */}
    </Container>
  );
};

export default Component;
```

### 3. 错误处理
```jsx
// 数据验证
if (!data || !Array.isArray(data)) {
  return (
    <Box centered p="xl">
      <Typography.Text color="error">数据格式错误</Typography.Text>
    </Box>
  );
}

// 安全访问
const safeTitle = item?.title || '默认标题';
```
