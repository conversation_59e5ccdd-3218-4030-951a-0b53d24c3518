{"compilerOptions": {"target": "ESNext", "outDir": "lib", "declarationDir": "types", "lib": ["ES2015", "ES2016", "ES2017", "ES2018", "ESNext", "DOM"], "module": "ESNext", "moduleResolution": "Node", "strictNullChecks": true, "esModuleInterop": true, "declaration": true, "declarationMap": false, "sourceMap": false, "stripInternal": true, "experimentalDecorators": true, "downlevelIteration": true, "jsx": "react", "jsxFactory": "React.createElement", "jsxFragmentFactory": "React.Fragment", "resolveJsonModule": true, "skipLibCheck": true}, "include": ["@types/*.ts", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}