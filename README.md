# @ali/hmod-pd-faq-ai-test

<p>
    <img src="https://img.alicdn.com/imgextra/i2/O1CN01yGauiD1MGujDwNzVk_!!6000000001408-2-tps-1413-392.png" alt="预览图" width="600" />
</p>

产品详情-常见问题组件，支持标签页展示分类列表，每个分类下展示常见问题的问答对列表，包含跳转卡片功能。

## 使用说明

> 运营和开发人员使用注意事项

## 相关资源

- PRD：待补充
- 设计稿：待补充

## 命令

本地调试

```bash
npm run dev
```

调试低代码配置

```bash
npm run dev:editor
```

代理调试线上页面中的组件

```bash
npm run proxy
```

## 开发相关

- ADC（aliyun-dot-com）基础组件库文档，请查看：[adc-design.alibaba-inc.com/](https://adc-design.alibaba-inc.com/)
