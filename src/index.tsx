import React from 'react';
import { Box, Container, Flex, Space, Tabs, Typography } from '@ali/adc';
import { ArrowRightOutlined } from '@ali/adc-icons';
import { Ackdistro } from '@ali/adc-product-icons';

import '@ali/adc/es/index.css';

import styles from './index.module.css';
import { FaqComponentProps } from './types';

const { H3, H6, Text } = Typography;

export default function FaqComponent(props: FaqComponentProps) {
  const { faqCategories, jumpCard } = props;

  return (
    <Container>
      <Flex gap="xl" sm={{ vertical: true, gap: 'lg' }}>
        {/* 左侧标签页 */}
        <Box flex={1}>
          <Tabs size="lg">
            <Tabs.List>
              {faqCategories.map((category, index) => (
                <Tabs.Tab key={index}>{category.categoryName}</Tabs.Tab>
              ))}
            </Tabs.List>
            <Tabs.Panels px={0}>
              {faqCategories.map((category, categoryIndex) => (
                <Tabs.Panel key={categoryIndex}>
                  <Space vertical gap="md">
                    {category.questions.map((qa, qaIndex) => (
                      <Box key={qaIndex} className={styles.questionItem}>
                        <Space vertical gap="xs">
                          <Flex gap="xs" align="flex-start">
                            <H6>Q：</H6>
                            <Text>{qa.question}</Text>
                          </Flex>
                          <Flex gap="xs" align="flex-start">
                            <H6>A：</H6>
                            <Flex gap="sm" align="flex-end" className={styles.answerContainer}>
                              <Text color="subtlest" className={styles.answerText}>
                                {qa.answer}
                              </Text>
                              {qa.jumpUrl && (
                                <a href={qa.jumpUrl} className={styles.detailLink}>
                                  <Text color="primary">查看详情</Text>
                                </a>
                              )}
                            </Flex>
                          </Flex>
                        </Space>
                      </Box>
                    ))}
                  </Space>
                </Tabs.Panel>
              ))}
            </Tabs.Panels>
          </Tabs>
        </Box>

        {/* 右侧跳转卡片 */}
        <Box width={300} sm={{ width: '100%' }}>
          <Box
            bordered
            shadowed
            p="lg"
            className={styles.jumpCard}
            onClick={() => window.open(jumpCard.jumpUrl, '_blank')}
          >
            <Space vertical gap="md" align="center">
              <Box>
                <Ackdistro className={styles.cardIcon} />
              </Box>
              <Box>
                <H3 textAlign="center">{jumpCard.title}</H3>
              </Box>
              <Box>
                <ArrowRightOutlined className={styles.arrowIcon} />
              </Box>
            </Space>
          </Box>
        </Box>
      </Flex>
    </Container>
  );
}
